import time
import json
import os
from datetime import datetime
from django.utils.deprecation import MiddlewareMixin
from loguru import logger


class ECGAnalysisLoggingMiddleware(MiddlewareMixin):
    """
    ECG分析API日志中间件
    捕获HTTP请求日志和响应信息，保存到专门的日志文件
    """
    
    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.setup_loggers()
    
    def setup_loggers(self):
        """设置专门的日志记录器"""
        today = datetime.now()
        
        # HTTP请求日志文件路径
        http_log_dir = os.path.join('logs', 'http_requests', str(today.year), str(today.month).zfill(2))
        os.makedirs(http_log_dir, exist_ok=True)
        http_log_file = os.path.join(http_log_dir, f"{str(today.day).zfill(2)}.log")
        
        # 心率分析数据日志文件路径
        analysis_log_dir = os.path.join('logs', 'analysis_data', str(today.year), str(today.month).zfill(2))
        os.makedirs(analysis_log_dir, exist_ok=True)
        analysis_log_file = os.path.join(analysis_log_dir, f"{str(today.day).zfill(2)}.log")
        
        # 创建HTTP请求日志记录器
        self.http_logger = logger.bind(name="http_requests")
        # 移除默认处理器，避免重复
        if not hasattr(self, '_http_logger_configured'):
            logger.add(
                http_log_file,
                format="[{time:DD/MMM/YYYY HH:mm:ss}] {message}",
                filter=lambda record: record["extra"].get("name") == "http_requests",
                rotation="10 MB",
                retention="30 days",
                level="INFO"
            )
            self._http_logger_configured = True
        
        # 创建心率分析数据日志记录器
        self.analysis_logger = logger.bind(name="analysis_data")
        if not hasattr(self, '_analysis_logger_configured'):
            logger.add(
                analysis_log_file,
                format="{time:YYYY-MM-DD HH:mm:ss} | {message}",
                filter=lambda record: record["extra"].get("name") == "analysis_data",
                rotation="10 MB",
                retention="30 days",
                level="INFO"
            )
            self._analysis_logger_configured = True
    
    def process_request(self, request):
        """处理请求开始时间"""
        request._start_time = time.time()
        return None
    
    def process_response(self, request, response):
        """处理响应并记录日志"""
        try:
            # 计算请求处理时间
            duration = time.time() - getattr(request, '_start_time', time.time())
            
            # 获取请求信息
            method = request.method
            path = request.path
            status_code = response.status_code
            content_length = len(response.content) if hasattr(response, 'content') else 0
            
            # 获取客户端IP
            client_ip = self.get_client_ip(request)
            
            # 记录HTTP请求日志（类似Apache/Nginx格式）
            log_message = f'"{method} {path} HTTP/1.1" {status_code} {content_length}'
            self.http_logger.info(log_message)
            
            # 如果是ECG分析API请求，记录额外的分析数据
            if path == '/api/diagnose/arrhythmia/' and method == 'POST':
                self.log_ecg_analysis_request(request, response, duration)
                
        except Exception as e:
            # 日志记录失败不应该影响正常响应
            pass
        
        return response
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def log_ecg_analysis_request(self, request, response, duration):
        """记录ECG分析请求的详细信息"""
        try:
            # 解析请求数据
            request_data = {}
            if hasattr(request, 'body') and request.body:
                try:
                    request_data = json.loads(request.body.decode('utf-8'))
                except:
                    pass
            
            # 解析响应数据
            response_data = {}
            if hasattr(response, 'content') and response.content:
                try:
                    response_data = json.loads(response.content.decode('utf-8'))
                except:
                    pass
            
            # 记录分析请求基本信息
            analysis_info = {
                'timestamp': datetime.now().isoformat(),
                'duration_ms': round(duration * 1000, 2),
                'status_code': response.status_code,
                'union_id': request_data.get('union_id', 'unknown'),
                'fs': request_data.get('fs'),
                'signal_length': len(request_data.get('signal', [])) if isinstance(request_data.get('signal'), list) else 'unknown'
            }
            
            self.analysis_logger.info(f"ECG Analysis Request: {json.dumps(analysis_info, ensure_ascii=False)}")
            
            # 如果响应成功，记录分析结果数据
            if response.status_code == 200 and response_data.get('code') == 0:
                self.log_analysis_results(response_data.get('data', {}))
                
        except Exception as e:
            # 分析日志记录失败不应该影响正常响应
            pass
    
    def log_analysis_results(self, analysis_data):
        """记录心率分析结果数据"""
        try:
            # 记录PQRSTC数据（包含心率信息）
            pqrstc = analysis_data.get('PQRSTC', {})
            if pqrstc:
                hr_data = {
                    'HR': pqrstc.get('HR', 0),
                    'QRS_duration': pqrstc.get('QRS_duration', 0),
                    'QT': pqrstc.get('QT', 0),
                    'QTc': pqrstc.get('QTc', 0)
                }
                self.analysis_logger.info(f"Heart Rate Data: {json.dumps(hr_data, ensure_ascii=False)}")
            
            # 记录诊断结果
            arrhythmia = analysis_data.get('ArrhythmiaDiagnosis', {})
            if arrhythmia:
                positive_diagnoses = [k for k, v in arrhythmia.items() if v == 1]
                if positive_diagnoses:
                    self.analysis_logger.info(f"Diagnosis Results: {', '.join(positive_diagnoses)}")
            
            # 记录健康指标
            health_metrics = analysis_data.get('HealthMetrics', {})
            if health_metrics:
                metrics_data = {
                    'Pressure': health_metrics.get('Pressure', 0),
                    'HRV': health_metrics.get('HRV', 0),
                    'Fatigue': health_metrics.get('Fatigue', 0),
                    'Vitality': health_metrics.get('Vitality', 0)
                }
                self.analysis_logger.info(f"Health Metrics: {json.dumps(metrics_data, ensure_ascii=False)}")
            
            # 记录信号质量信息
            signal_quality = {
                'SignalQuantity': analysis_data.get('SignalQuantity'),
                'IsNoise': analysis_data.get('IsNoise', False),
                'NoiseMessage': analysis_data.get('NoiseMessage', '')
            }
            self.analysis_logger.info(f"Signal Quality: {json.dumps(signal_quality, ensure_ascii=False)}")
            
        except Exception as e:
            # 结果日志记录失败不应该影响正常响应
            pass
