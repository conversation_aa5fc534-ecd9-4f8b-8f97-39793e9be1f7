import numpy as np
import h5py
import tensorflow as tf
from tensorflow.keras import layers, Model, regularizers, callbacks
from scipy import signal
import os
import datetime
import gc
import math
from contextlib import contextmanager

# 设置混合精度训练 - 暂时禁用以避免兼容性问题
# policy = tf.keras.mixed_precision.Policy('mixed_float16')
# tf.keras.mixed_precision.set_global_policy(policy)

# 改进的SE残差块 - 动态reduction ratio
def improved_se_residual_block(x, filters, kernel_size=7, stride=1, dropout_rate=0.2):
    shortcut = x
    
    # 动态调整reduction ratio
    reduction_ratio = max(4, min(16, filters // 8))
    
    # 主路径
    x = layers.Conv1D(filters, kernel_size, strides=stride, padding='same', 
                     kernel_regularizer=regularizers.l2(1e-4))(x)
    x = layers.BatchNormalization()(x)
    x = layers.Activation('relu')(x)
    x = layers.Dropout(dropout_rate)(x)
    
    x = layers.Conv1D(filters, kernel_size, padding='same', 
                     kernel_regularizer=regularizers.l2(1e-4))(x)
    x = layers.BatchNormalization()(x)
    
    # SE模块
    se = layers.GlobalAveragePooling1D()(x)
    se = layers.Dense(filters // reduction_ratio, activation='relu', 
                     kernel_regularizer=regularizers.l2(1e-4))(se)
    se = layers.Dense(filters, activation='sigmoid', 
                     kernel_regularizer=regularizers.l2(1e-4))(se)
    se = layers.Reshape((1, filters))(se)
    x = layers.Multiply()([x, se])
    
    # 残差连接
    if stride != 1 or shortcut.shape[-1] != filters:
        shortcut = layers.Conv1D(filters, 1, strides=stride, padding='same', 
                                kernel_regularizer=regularizers.l2(1e-4))(shortcut)
        shortcut = layers.BatchNormalization()(shortcut)
    
    x = layers.Add()([shortcut, x])
    x = layers.Activation('relu')(x)
    return x

# 位置编码层
class PositionalEncoding(layers.Layer):
    def __init__(self, max_len=5000, d_model=128, **kwargs):
        super().__init__(**kwargs)
        self.max_len = max_len
        self.d_model = d_model
        
    def build(self, input_shape):
        pe = np.zeros((self.max_len, self.d_model))
        position = np.arange(0, self.max_len).reshape(-1, 1)
        div_term = np.exp(np.arange(0, self.d_model, 2) * -(math.log(10000.0) / self.d_model))
        
        pe[:, 0::2] = np.sin(position * div_term)
        pe[:, 1::2] = np.cos(position * div_term)
        
        self.pe = self.add_weight(
            name='positional_encoding',
            shape=(self.max_len, self.d_model),
            initializer='zeros',
            trainable=False
        )
        self.pe.assign(pe)
        super().build(input_shape)
    
    def call(self, inputs):
        seq_len = tf.shape(inputs)[1]
        return inputs + self.pe[:seq_len, :]
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "max_len": self.max_len,
            "d_model": self.d_model
        })
        return config

# 改进的多头自注意力 - 添加位置编码
class ImprovedMultiHeadSelfAttention(layers.Layer):
    def __init__(self, embed_dim, num_heads=8, dropout_rate=0.1, **kwargs):
        super().__init__(**kwargs)
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.dropout_rate = dropout_rate
        assert embed_dim % num_heads == 0
        self.projection_dim = embed_dim // num_heads
        
        self.query_dense = layers.Dense(embed_dim)
        self.key_dense = layers.Dense(embed_dim)
        self.value_dense = layers.Dense(embed_dim)
        self.combine_heads = layers.Dense(embed_dim)
        self.dropout = layers.Dropout(dropout_rate)
        self.pos_encoding = PositionalEncoding(d_model=embed_dim)
        
    def attention(self, query, key, value):
        score = tf.matmul(query, key, transpose_b=True)
        dim_key = tf.cast(tf.shape(key)[-1], score.dtype)  # 使用score的数据类型
        scaled_score = score / tf.math.sqrt(dim_key)
        weights = tf.nn.softmax(scaled_score, axis=-1)
        weights = self.dropout(weights)
        output = tf.matmul(weights, value)
        return output
    
    def separate_heads(self, x, batch_size):
        x = tf.reshape(x, (batch_size, -1, self.num_heads, self.projection_dim))
        return tf.transpose(x, perm=[0, 2, 1, 3])
    
    def call(self, inputs, training=None):
        # 添加位置编码
        inputs = self.pos_encoding(inputs)
        
        batch_size = tf.shape(inputs)[0]
        query = self.query_dense(inputs)
        key = self.key_dense(inputs)
        value = self.value_dense(inputs)
        
        query = self.separate_heads(query, batch_size)
        key = self.separate_heads(key, batch_size)
        value = self.separate_heads(value, batch_size)
        
        attention = self.attention(query, key, value)
        attention = tf.transpose(attention, perm=[0, 2, 1, 3])
        concat_attention = tf.reshape(attention, (batch_size, -1, self.embed_dim))
        output = self.combine_heads(concat_attention)
        return output
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "embed_dim": self.embed_dim,
            "num_heads": self.num_heads,
            "dropout_rate": self.dropout_rate
        })
        return config

# 改进的分布感知损失 - 更精细的权重调整
def improved_distribution_aware_loss(y_true, y_pred):
    # 确保数据类型一致
    y_true = tf.cast(y_true, tf.float32)
    y_pred = tf.cast(y_pred, tf.float32)

    mae = tf.abs(y_true - y_pred)

    # 年龄段权重 - 更精细的分段，确保数据类型一致
    age_weights = tf.where(
        y_true < 25, tf.cast(2.0, tf.float32),  # 年轻人权重更高
        tf.where(
            y_true < 35, tf.cast(1.5, tf.float32),
            tf.where(
                y_true < 50, tf.cast(1.0, tf.float32),  # 中年人基准权重
                tf.where(
                    y_true < 65, tf.cast(1.3, tf.float32),
                    tf.cast(1.8, tf.float32)  # 老年人权重更高
                )
            )
        )
    )

    # 误差权重 - 渐进式权重，确保数据类型一致
    error_weights = tf.where(
        mae < 5.0, tf.cast(1.0, tf.float32),
        tf.where(
            mae < 8.0, tf.cast(1.2, tf.float32),
            tf.where(
                mae < 12.0, tf.cast(1.5, tf.float32),
                tf.cast(2.0, tf.float32)  # 大误差严重惩罚
            )
        )
    )

    # Huber损失组件 - 对异常值更鲁棒，确保数据类型一致
    huber_delta = tf.cast(1.0, tf.float32)
    huber_loss = tf.where(
        mae <= huber_delta,
        tf.cast(0.5, tf.float32) * mae ** 2,
        huber_delta * mae - tf.cast(0.5, tf.float32) * huber_delta ** 2
    )

    final_weights = age_weights * error_weights
    weighted_loss = huber_loss * final_weights

    return tf.reduce_mean(weighted_loss)

# Warmup + CosineDecay学习率调度
class WarmupCosineDecay(tf.keras.optimizers.schedules.LearningRateSchedule):
    def __init__(self, initial_learning_rate, decay_steps, warmup_steps=1000, alpha=0.0):
        super().__init__()
        self.initial_learning_rate = initial_learning_rate
        self.decay_steps = decay_steps
        self.warmup_steps = warmup_steps
        self.alpha = alpha
        
    def __call__(self, step):
        # Warmup阶段
        warmup_lr = self.initial_learning_rate * (step / self.warmup_steps)
        
        # CosineDecay阶段
        cosine_decay_lr = tf.keras.optimizers.schedules.CosineDecay(
            self.initial_learning_rate,
            self.decay_steps - self.warmup_steps,
            alpha=self.alpha
        )(step - self.warmup_steps)
        
        return tf.where(step < self.warmup_steps, warmup_lr, cosine_decay_lr)
    
    def get_config(self):
        return {
            "initial_learning_rate": self.initial_learning_rate,
            "decay_steps": self.decay_steps,
            "warmup_steps": self.warmup_steps,
            "alpha": self.alpha
        }

# 内存优化的数据生成器 - 懒加载
class OptimizedECGDataGenerator(tf.keras.utils.Sequence):
    def __init__(self, file_paths, batch_size=64, is_training=True, cache_size=3):
        self.file_paths = file_paths
        self.batch_size = batch_size
        self.is_training = is_training
        self.cache_size = cache_size  # 最多同时缓存的文件数
        
        # 文件索引映射
        self.file_indices = []
        self.file_sample_counts = []
        
        # 预扫描文件获取索引信息
        for file_idx, fp in enumerate(file_paths):
            with h5py.File(fp, 'r') as f:
                ages = f['ages'][:]
                valid_indices = np.where((ages >= 18) & (ages <= 80))[0]
                self.file_indices.extend([(file_idx, idx) for idx in valid_indices])
                self.file_sample_counts.append(len(valid_indices))
        
        self.indices = np.arange(len(self.file_indices))
        if self.is_training:
            np.random.shuffle(self.indices)
            
        # 文件缓存
        self.file_cache = {}
        self.cache_order = []
        
        print(f"OptimizedECGDataGenerator: 总样本数 {len(self.indices)}")
    
    @contextmanager
    def get_file_handle(self, file_idx):
        """获取文件句柄，使用LRU缓存"""
        file_path = self.file_paths[file_idx]
        
        if file_idx in self.file_cache:
            # 更新缓存顺序
            self.cache_order.remove(file_idx)
            self.cache_order.append(file_idx)
            yield self.file_cache[file_idx]
        else:
            # 打开新文件
            if len(self.file_cache) >= self.cache_size:
                # 移除最旧的文件
                oldest_file_idx = self.cache_order.pop(0)
                self.file_cache[oldest_file_idx].close()
                del self.file_cache[oldest_file_idx]
            
            # 添加新文件到缓存
            file_handle = h5py.File(file_path, 'r')
            self.file_cache[file_idx] = file_handle
            self.cache_order.append(file_idx)
            yield file_handle
    
    def __len__(self):
        return int(np.ceil(len(self.indices) / self.batch_size))
    
    def __getitem__(self, idx):
        batch_indices = self.indices[idx * self.batch_size : (idx + 1) * self.batch_size]
        batch_data = []
        batch_ages = []
        target_length = 5000
        
        for i in batch_indices:
            file_idx, sample_idx = self.file_indices[i]
            
            with self.get_file_handle(file_idx) as f:
                data = f['ecg_data'][sample_idx]
                age = f['ages'][sample_idx]
            
            # 改进的预处理
            data = self._advanced_preprocess(data, target_length)
            batch_data.append(data)
            batch_ages.append(np.clip(age, 18.0, 80.0))
        
        batch_data = np.expand_dims(np.array(batch_data, dtype=np.float32), axis=-1)
        batch_ages = np.array(batch_ages, dtype=np.float32)
        
        return batch_data, batch_ages
    
    def _advanced_preprocess(self, data, target_length):
        """改进的预处理流程"""
        # 异常值处理
        data = np.clip(data, -10, 10)
        
        # 基线漂移校正 - 使用更大的核
        baseline = signal.medfilt(data, kernel_size=min(201, len(data)//10*2+1))
        data = data - baseline
        
        # 长度调整
        if len(data) > target_length:
            # 随机裁剪（训练时）或中心裁剪（验证时）
            if self.is_training:
                start_idx = np.random.randint(0, len(data) - target_length + 1)
                data = data[start_idx:start_idx + target_length]
            else:
                start_idx = (len(data) - target_length) // 2
                data = data[start_idx:start_idx + target_length]
        elif len(data) < target_length:
            # 对称填充
            pad_width = target_length - len(data)
            pad_left = pad_width // 2
            pad_right = pad_width - pad_left
            data = np.pad(data, (pad_left, pad_right), 'reflect')
        
        # 自适应标准化
        data_std = np.std(data)
        if data_std > 1e-6:
            data = (data - np.mean(data)) / data_std
        
        return data
    
    def on_epoch_end(self):
        if self.is_training:
            np.random.shuffle(self.indices)
    
    def close(self):
        """关闭所有文件句柄"""
        for f in self.file_cache.values():
            f.close()
        self.file_cache.clear()
        self.cache_order.clear()

# 改进的模型构建
def create_advanced_ecg_age_model(input_shape=(5000, 1)):
    """创建改进的ECG年龄预测模型"""
    inputs = layers.Input(shape=input_shape)

    # 初始卷积层 - 更大的感受野
    x = layers.Conv1D(64, 21, strides=2, padding='same',
                     kernel_regularizer=regularizers.l2(1e-4))(inputs)
    x = layers.BatchNormalization()(x)
    x = layers.Activation('relu')(x)
    x = layers.MaxPooling1D(2)(x)

    # 第一组残差块
    x = improved_se_residual_block(x, 64, dropout_rate=0.1)
    x = improved_se_residual_block(x, 64, dropout_rate=0.1)
    x = layers.MaxPooling1D(2)(x)

    # 第二组残差块
    x = improved_se_residual_block(x, 128, stride=2, dropout_rate=0.15)
    x = improved_se_residual_block(x, 128, dropout_rate=0.15)
    x = layers.MaxPooling1D(2)(x)

    # 注意力机制层
    x = ImprovedMultiHeadSelfAttention(embed_dim=128, num_heads=8, dropout_rate=0.1)(x)

    # 第三组残差块
    x = improved_se_residual_block(x, 256, stride=2, dropout_rate=0.2)
    x = improved_se_residual_block(x, 256, dropout_rate=0.2)
    x = layers.MaxPooling1D(2)(x)

    # 第四组残差块 - 增加深度
    x = improved_se_residual_block(x, 512, stride=2, dropout_rate=0.25)
    x = improved_se_residual_block(x, 512, dropout_rate=0.25)

    # 多尺度特征融合
    gap = layers.GlobalAveragePooling1D()(x)
    gmp = layers.GlobalMaxPooling1D()(x)

    # 添加统计特征
    std_pool = layers.Lambda(lambda x: tf.math.reduce_std(x, axis=1))(x)
    concat_features = layers.Concatenate()([gap, gmp, std_pool])

    # 全连接层 - 渐进式降维
    x = layers.Dense(1024, kernel_regularizer=regularizers.l2(1e-4))(concat_features)
    x = layers.BatchNormalization()(x)
    x = layers.Activation('relu')(x)
    x = layers.Dropout(0.5)(x)

    x = layers.Dense(512, kernel_regularizer=regularizers.l2(1e-4))(x)
    x = layers.BatchNormalization()(x)
    x = layers.Activation('relu')(x)
    x = layers.Dropout(0.4)(x)

    x = layers.Dense(256, kernel_regularizer=regularizers.l2(1e-4))(x)
    x = layers.BatchNormalization()(x)
    x = layers.Activation('relu')(x)
    x = layers.Dropout(0.3)(x)

    # 输出层
    outputs = layers.Dense(1)(x)  # 暂时移除混合精度相关设置
    outputs = layers.Lambda(lambda x: 18.0 + tf.nn.sigmoid(x) * 62.0)(outputs)

    model = Model(inputs=inputs, outputs=outputs)
    return model

# 梯度累积训练器
class GradientAccumulationTrainer:
    def __init__(self, model, optimizer, loss_fn, accumulation_steps=4):
        self.model = model
        self.optimizer = optimizer
        self.loss_fn = loss_fn
        self.accumulation_steps = accumulation_steps

        # 梯度累积变量
        self.accumulated_gradients = []
        for var in self.model.trainable_variables:
            self.accumulated_gradients.append(tf.Variable(tf.zeros_like(var), trainable=False))

    @tf.function
    def accumulate_gradients(self, x, y):
        """累积梯度"""
        with tf.GradientTape() as tape:
            predictions = self.model(x, training=True)
            loss = self.loss_fn(y, predictions) / self.accumulation_steps

        # 计算梯度（暂时禁用混合精度）
        gradients = tape.gradient(loss, self.model.trainable_variables)

        # 累积梯度
        for i, grad in enumerate(gradients):
            if grad is not None:
                self.accumulated_gradients[i].assign_add(grad)

        return loss

    @tf.function
    def apply_gradients(self):
        """应用累积的梯度"""
        self.optimizer.apply_gradients(zip(self.accumulated_gradients, self.model.trainable_variables))

        # 重置累积梯度
        for accumulated_grad in self.accumulated_gradients:
            accumulated_grad.assign(tf.zeros_like(accumulated_grad))

# 改进的模型编译
def compile_advanced_model(model, total_steps):
    """编译改进的模型"""
    # Warmup + CosineDecay学习率
    lr_schedule = WarmupCosineDecay(
        initial_learning_rate=2e-3,
        decay_steps=total_steps,
        warmup_steps=total_steps // 20,  # 5%的步数用于warmup
        alpha=1e-6
    )

    # 使用Adam优化器（TensorFlow 2.6.0兼容）
    optimizer = tf.keras.optimizers.Adam(
        learning_rate=lr_schedule,
        beta_1=0.9,
        beta_2=0.999,
        epsilon=1e-7,
        amsgrad=True
    )
    # 注意：TensorFlow 2.6.0没有AdamW，使用Adam + L2正则化代替

    model.compile(
        optimizer=optimizer,
        loss=improved_distribution_aware_loss,
        metrics=[
            'mae',
            tf.keras.metrics.RootMeanSquaredError(name='rmse'),
            tf.keras.metrics.MeanAbsolutePercentageError(name='mape'),
            tf.keras.metrics.MeanSquaredError(name='mse')
        ]
    )
    return model

# 高级回调函数
def create_advanced_callbacks(model_name="advanced_ecg_age_model"):
    """创建改进的回调函数"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    model_dir = f"./models/{model_name}_{timestamp}"
    os.makedirs(model_dir, exist_ok=True)

    callbacks_list = [
        # 早停 - 更严格的条件
        callbacks.EarlyStopping(
            monitor='val_mae',
            patience=15,
            restore_best_weights=True,
            min_delta=0.02,
            verbose=1
        ),

        # 学习率衰减 - 作为备用
        callbacks.ReduceLROnPlateau(
            monitor='val_mae',
            factor=0.3,
            patience=8,
            min_lr=1e-8,
            verbose=1,
            cooldown=3
        ),

        # 模型检查点 - 保存多个版本
        callbacks.ModelCheckpoint(
            os.path.join(model_dir, 'best_model.h5'),
            monitor='val_mae',
            save_best_only=True,
            mode='min',
            verbose=1,
            save_weights_only=False
        ),

        # 训练日志
        callbacks.CSVLogger(
            os.path.join(model_dir, 'training_log.csv'),
            separator=',',
            append=False
        ),

        # TensorBoard日志
        callbacks.TensorBoard(
            log_dir=os.path.join(model_dir, 'tensorboard'),
            histogram_freq=1,
            write_graph=True,
            write_images=False,
            update_freq='epoch'
        ),

        # 自定义评估回调
        CustomEvaluationCallback(model_dir)
    ]

    return callbacks_list, model_dir

# 自定义评估回调
class CustomEvaluationCallback(callbacks.Callback):
    def __init__(self, model_dir):
        super().__init__()
        self.model_dir = model_dir
        self.best_mae = float('inf')

    def on_epoch_end(self, epoch, logs=None):
        logs = logs or {}

        # 记录详细评估指标
        val_mae = logs.get('val_mae', 0)
        val_rmse = logs.get('val_rmse', 0)
        val_mape = logs.get('val_mape', 0)

        # 保存最佳模型的额外信息
        if val_mae < self.best_mae:
            self.best_mae = val_mae

            # 保存评估报告
            report = {
                'epoch': epoch + 1,
                'val_mae': float(val_mae),
                'val_rmse': float(val_rmse),
                'val_mape': float(val_mape),
                'timestamp': datetime.datetime.now().isoformat()
            }

            import json
            with open(os.path.join(self.model_dir, 'best_model_report.json'), 'w') as f:
                json.dump(report, f, indent=2)

        # 打印详细信息
        print(f"\nEpoch {epoch + 1} - Val MAE: {val_mae:.4f}, Val RMSE: {val_rmse:.4f}, Val MAPE: {val_mape:.2f}%")

# 主训练函数
def train_advanced_model(train_files, val_files, epochs=50, batch_size=32):
    """改进的训练主函数"""
    print("=== 开始训练改进的ECG年龄预测模型 ===")

    # 创建数据生成器
    train_gen = OptimizedECGDataGenerator(train_files, batch_size=batch_size, is_training=True)
    val_gen = OptimizedECGDataGenerator(val_files, batch_size=batch_size, is_training=False)

    # 计算总训练步数
    steps_per_epoch = len(train_gen)
    total_steps = steps_per_epoch * epochs

    # 创建模型
    model = create_advanced_ecg_age_model()
    model = compile_advanced_model(model, total_steps)

    # 打印模型信息
    model.summary()
    print(f"总参数量: {model.count_params():,}")

    # 创建回调
    callbacks_list, model_dir = create_advanced_callbacks()

    try:
        # 开始训练
        history = model.fit(
            train_gen,
            epochs=epochs,
            validation_data=val_gen,
            callbacks=callbacks_list,
            verbose=1,
            workers=4,
            use_multiprocessing=True,
            max_queue_size=20
        )

        print(f"\n=== 训练完成！模型保存在: {model_dir} ===")
        return history, model_dir

    except KeyboardInterrupt:
        print("\n训练被用户中断")
        return None, model_dir

    finally:
        # 清理资源
        train_gen.close()
        val_gen.close()
        gc.collect()

# 使用示例
if __name__ == "__main__":
    # 设置数据目录路径
    DATA_DIR = '/opt/jupyter_notebook_workspace/data/TrainData/ecg_lead_i'

    # 自动查找所有H5文件并划分训练集和验证集
    import glob
    import random

    all_files = glob.glob(f"{DATA_DIR}/*.hdf5")
    if not all_files:
        print(f"错误：在目录 {DATA_DIR} 中没有找到任何.hdf5文件")
        exit(1)

    print(f"找到 {len(all_files)} 个数据文件")

    # 随机打乱文件列表
    random.seed(42)  # 设置随机种子确保可重复性
    random.shuffle(all_files)

    # 按8:2比例划分训练集和验证集
    split_idx = int(len(all_files) * 0.8)
    train_files = all_files[:split_idx]
    val_files = all_files[split_idx:]

    print(f"训练文件: {len(train_files)} 个")
    print(f"验证文件: {len(val_files)} 个")

    # 开始训练
    history, model_dir = train_advanced_model(
        train_files=train_files,
        val_files=val_files,
        epochs=50,
        batch_size=32
    )
