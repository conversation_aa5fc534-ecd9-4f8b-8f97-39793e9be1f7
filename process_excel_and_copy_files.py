import pandas as pd
import os
import shutil
from pathlib import Path

def process_excel_and_copy_files():
    # 文件路径
    excel_path = r"D:\ECG\0723一分钟项目测试\标注平台数据\数据\窦性心律诊断为过缓\工作簿1.xlsx"
    source_folder = r"D:\ECG\0723一分钟项目测试\标注平台数据\数据"
    target_folder = r"D:\ECG\0723一分钟项目测试\标注平台数据\数据\窦性心律诊断为过缓"
    
    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path)
        
        # 显示原始数据的前几行
        print("\n原始数据预览:")
        print(df.head())
        print(f"\n数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 假设第一列是数据来源列
        first_column = df.columns[0]
        print(f"\n处理第一列: {first_column}")
        
        # 创建目标文件夹（如果不存在）
        os.makedirs(target_folder, exist_ok=True)
        
        copied_files = []
        not_found_files = []
        
        # 处理每一行数据
        for index, row in df.iterrows():
            if pd.isna(row[first_column]):
                continue
                
            original_filename = str(row[first_column]).strip()
            print(f"\n处理第{index+1}行: {original_filename}")
            
            # 将.csv文件名改为.json
            if original_filename.endswith('_acc.csv'):
                json_filename = original_filename.replace('_acc.csv', '.json')
            elif original_filename.endswith('.csv'):
                json_filename = original_filename.replace('.csv', '.json')
            else:
                # 如果不是csv文件，跳过
                print(f"跳过非CSV文件: {original_filename}")
                continue
            
            print(f"对应的JSON文件名: {json_filename}")
            
            # 更新Excel中的文件名
            df.at[index, first_column] = json_filename
            
            # 查找并复制对应的JSON文件
            source_file_path = os.path.join(source_folder, json_filename)
            target_file_path = os.path.join(target_folder, json_filename)
            
            if os.path.exists(source_file_path):
                try:
                    shutil.copy2(source_file_path, target_file_path)
                    copied_files.append(json_filename)
                    print(f"✓ 成功复制: {json_filename}")
                except Exception as e:
                    print(f"✗ 复制失败 {json_filename}: {e}")
            else:
                not_found_files.append(json_filename)
                print(f"✗ 文件不存在: {json_filename}")
        
        # 保存更新后的Excel文件
        output_excel_path = excel_path.replace('.xlsx', '_updated.xlsx')
        df.to_excel(output_excel_path, index=False)
        print(f"\n✓ 更新后的Excel文件已保存: {output_excel_path}")
        
        # 显示处理结果
        print(f"\n=== 处理结果 ===")
        print(f"成功复制的文件数量: {len(copied_files)}")
        print(f"未找到的文件数量: {len(not_found_files)}")
        
        if copied_files:
            print(f"\n成功复制的文件:")
            for file in copied_files[:10]:  # 只显示前10个
                print(f"  - {file}")
            if len(copied_files) > 10:
                print(f"  ... 还有 {len(copied_files) - 10} 个文件")
        
        if not_found_files:
            print(f"\n未找到的文件:")
            for file in not_found_files[:10]:  # 只显示前10个
                print(f"  - {file}")
            if len(not_found_files) > 10:
                print(f"  ... 还有 {len(not_found_files) - 10} 个文件")
        
        print(f"\n处理完成！")
        
    except FileNotFoundError as e:
        print(f"错误: 找不到文件 - {e}")
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    process_excel_and_copy_files()
