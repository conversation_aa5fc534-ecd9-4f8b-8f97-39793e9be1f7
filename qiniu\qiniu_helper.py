import json
import traceback
from qiniu import Auth
from global_settings import QINIU


def get_qiniu_data(file_path, environment):
    try:
        access_key = QINIU[environment]['access_key']
        secret_key = QINIU[environment]['secret_key']
        domain_prefix = QINIU[environment]['domain_prefix']  # 存储空间域名前缀
        # 构建鉴权对象
        q = Auth(access_key, secret_key)

        # 构建私有空间的下载链接
        private_url = q.private_download_url(domain_prefix + file_path)

        # 使用requests或其他库下载文件
        import requests
        response = requests.get(private_url)

        if response.status_code == 200:
            return json.loads(response.content)

        return None
    except Exception as e:
        print(traceback.print_exc())
        return None
