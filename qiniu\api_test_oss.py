import ast
import time

import numpy as np
from biosppy.signals.ecg import ecg

from common.api_ecg_analysis import ecg_analysis
from common.filtering import wavelet_filter
from common.image_helper import merge_image
from utils.generate_ecg import output_image
from utils.qiniu_helper import get_qiniu_data

if __name__ == '__main__':
    sample_rate = 250
    # url = 'http://192.168.0.158:8000'
    url = 'http://127.0.0.1:8001'
    # url = 'http://ecggpt.aiweihe.com'
    # url = 'http://ecggpt.test.aiweihe.com'

    # es_key = 'CUSTOMER16678053292851298/20250630182521'
    # es_key = 'CUSTOMER19302122416362659846061/20250626034735'
    # es_key = 'CUSTOMER18838092628219043846502/20250626134026'
    es_key = 'CUSTOMER17149643121023643/20250202133738'     # 正常心电图
    # es_key = 'CUSTOMER16678053292851298/20250630182521'     # 平直线

    # es_key = 'CUSTOMER18838092628219043846502/20250629181745'     # 低频噪音

    # es_key = 'CUSTOMER18838092628219043846502/20250717033838'
    # if 'ecgII' in info.keys() and info['ecgII']:
    #     ecg_data = info['ecgII']
    # else:
    #     ecg_data = info['ecg']

    type = '/ecg/'

    # type = '/ecgError/'

    # es_key = 'CUSTOMER19434897676702228481471/20250719093828'
    # es_key = 'CUSTOMER18838092628219043846502/20250723013818'
    # es_key = 'CUSTOMER18838092628219043846502/20250722220518'
    # es_key = 'CUSTOMER17149643121023643/20250202133738'  # 正常心电图

    # todo 待处理未穿戴未被识别为噪音的数据
    # es_key = 'CUSTOMER18838092628219043846502/20250726224617'
    # es_key = 'CUSTOMER18838092628219043846502/20250726225117'
    # es_key = 'CUSTOMER18838092628219043846502/20250726225717'

    # es_key = 'CUSTOMER18838092628219043846502/20250801115243'


    # es_key = 'CUSTOMER18838092628219043846502/20250726213114'

    info = get_qiniu_data(type + es_key, 'prod')
    ecg_data_str = info['ecg']
    ecg_data = np.array(ast.literal_eval(ecg_data_str))

    # for i in range(10, 11):
    start = time.time()
    result = ecg_analysis(ecg_data_str, sample_rate, url, ecg_age_key=0, health_metrics=0, window_sec=10, step_sec=10)
    stop = time.time()
    print(f'cost time: {(stop - start):.2f}S')
    print(result)

    # signal_time_period = result['SignalTimePeriod'].split('-')
    #
    # start_time = int(signal_time_period[0])
    # end_time = int(signal_time_period[1])

    # ecg_data = ecg_data[start_time * sample_rate: end_time * sample_rate:]
    #
    # ecg_info = ecg(ecg_data, sample_rate)
    #
    # print(1)


    # img_name = es_key.split('/')[1]
    # img_path1 = output_image(ecg_data, sample_rate, file_name=img_name)
    # img_path2 = output_image(wavelet_filter(ecg_data, sample_rate), sample_rate, file_name=f'{img_name}_filter')
    # merge_image(img_path1, img_path2, f'{img_name}_merge')


    # es_key_list = [
    #     'CUSTOMER18838092628219043846502/20250626134026',
    #     'CUSTOMER19302122416362659846061/20250626034735',
    #     'CUSTOMER18838092628219043846502/20250626130400',
    #     'CUSTOMER19363604285708738560030/20250625230745', # 待确认
    #
    #     'CUSTOMER19377421495812136960732/20250627025657',
    #     'CUSTOMER19377421495812136960732/20250626131638',
    #
    #     'CUSTOMER17149643121023643/20250202133738'  # 正常心电图
    # ]
    #
    # for es_key in es_key_list:
    #     # if es_key == 'CUSTOMER19377421495812136960732/20250626131638':
    #         info = get_qiniu_data(es_key, 'prod')
    #         ecg_data_str = info['ecg']
    #
    #         ecg_data = ast.literal_eval(ecg_data_str)
    #         img_name = es_key.split('/')[1]
    #         # img_path1 = output_image(ecg_data, sample_rate, file_name=img_name)
    #         # img_path2 = output_image(wavelet_filter(ecg_data, sample_rate), sample_rate, file_name=f'{img_name}_filter')
    #         # merge_image(img_path1, img_path2, f'{img_name}_merge')
    #
    #         result = ecg_analysis(ecg_data_str, sample_rate, url, ecg_age_key=0, health_metrics=0)
    #         print(result)

