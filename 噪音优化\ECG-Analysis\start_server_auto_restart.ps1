# PowerShell script to run and monitor Django server

$ScriptToRun = "python manage.py runserver"
# !!! 重要: 请将下面的路径修改为您Django项目的实际根目录 !!!
$WorkingDirectory = "D:\Project\噪音优化\ECG-Analysis"

Write-Host "Monitoring and auto-restarting Django server."
Write-Host "Working Directory: $WorkingDirectory"
Write-Host "Script to run: $ScriptToRun"
Write-Host "Press Ctrl+C to stop this monitoring script."
Write-Host "--------------------------------------------------"

while ($true) {
    Write-Host "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Starting Django server..."

    $process = Start-Process python -ArgumentList "manage.py", "runserver" -WorkingDirectory $WorkingDirectory -PassThru -NoNewWindow -Wait -ErrorAction SilentlyContinue

    if ($process.ExitCode -ne 0) {
        Write-Warning "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Django server process exited with code $($process.ExitCode)."
        Write-Host "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Restarting in 5 seconds..."
        Start-Sleep -Seconds 5
    } else {
        Write-Host "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Django server process exited cleanly (code 0). Restarting in 5 seconds to ensure it stays up (or if it was manually stopped)."
        Start-Sleep -Seconds 5 # 即使正常退出也重启，除非脚本被Ctrl+C停止
    }
}