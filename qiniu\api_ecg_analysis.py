import json

import requests

import global_settings


def get_token(url):
    login_url = f'{url}/api/login/'
    client_id = global_settings.heartVoice['login']['clientId']
    client_secret = global_settings.heartVoice['login']['clientSecret']

    params = {
        "clientId": client_id,
        "clientSecret": client_secret
    }
    resp = requests.post(login_url, data=params)
    resp_json = resp.json()

    data = resp_json.get("data")
    return data.get("token")


def ecg_analysis(ecg_data, fs, url, gravity=None, ecg_age_key=1, health_metrics=1, window_sec=5, step_sec=1):
    analysis_url = f'{url}/api/diagnose/arrhythmia/'
    headers = {
        "X-Auth-Token": get_token(url),
        "Content-Type": "application/json;charset=utf-8"
    }

    params = {
        "signal": ecg_data,
        "fs": fs,
        "adc_gain": 1,
        "adc_zero": 0,
        'window_sec': window_sec,
        'step_sec': step_sec,
        "union_id": "",
        'ecg_age_key': ecg_age_key,
        'health_metrics': health_metrics
    }

    if gravity is not None:
        params['gravity'] = gravity

    resp = requests.post(analysis_url, headers=headers, data=json.dumps(params))

    if resp.status_code == 200:
        resp_json = resp.json()

        if resp_json["code"] == 0:
            return resp_json["data"]
        else:
            return resp_json
    else:
        return resp.status_code


def get_conclusion(result_data):
    """
    获取结论中的数据
    :param result_data:
    :return:
    """
    if result_data['SignalQuantity'] == -1:
        return '噪音'
    else:
        conclusion_list = [key for key, value in result_data['ArrhythmiaDiagnosis'].items() if
                           value == 1]

        if len(conclusion_list) == 0:
            return ''
        else:
            return ','.join(conclusion_list) if len(conclusion_list) > 1 else conclusion_list[0]