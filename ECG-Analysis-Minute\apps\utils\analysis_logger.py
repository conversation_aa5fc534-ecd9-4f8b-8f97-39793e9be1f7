import os
import json
from datetime import datetime
from loguru import logger


class ECGAnalysisDataLogger:
    """
    ECG分析数据专用日志记录器
    用于记录心率分析过程中的详细数据输出
    """
    
    def __init__(self):
        self.setup_logger()
    
    def setup_logger(self):
        """设置分析数据日志记录器"""
        today = datetime.now()
        
        # 分析数据日志文件路径
        log_dir = os.path.join('logs', 'ecg_analysis', str(today.year), str(today.month).zfill(2))
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, f"{str(today.day).zfill(2)}.log")
        
        # 创建专用的日志记录器
        self.logger = logger.bind(name="ecg_analysis_data")
        
        # 添加文件处理器（只有在未配置时才添加）
        if not hasattr(self.__class__, '_logger_configured'):
            logger.add(
                log_file,
                format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {message}",
                filter=lambda record: record["extra"].get("name") == "ecg_analysis_data",
                rotation="50 MB",
                retention="30 days",
                level="INFO",
                encoding="utf-8"
            )
            self.__class__._logger_configured = True
    
    def log_heart_rate_analysis(self, analysis_data, union_id=None):
        """
        记录心率分析数据
        
        Args:
            analysis_data: 分析结果数据字典
            union_id: 用户ID
        """
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 记录基本信息
            if union_id:
                self.logger.info(f"=== ECG Analysis Start for User: {union_id} ===")
            
            # 记录心率相关数据
            pqrstc = analysis_data.get('PQRSTC', {})
            if pqrstc:
                hr = pqrstc.get('HR', 0)
                qrs_duration = pqrstc.get('QRS_duration', 0)
                qt = pqrstc.get('QT', 0)
                qtc = pqrstc.get('QTc', 0)
                
                # 模拟原始终端输出格式的心率数据
                self.logger.info(f"1. max_hr: {hr}, min_hr: {hr}, hr: {hr} bpm")
                self.logger.info(f"2. QRS Duration: {qrs_duration} ms")
                self.logger.info(f"3. QT Interval: {qt} ms, QTc: {qtc} ms")
            
            # 记录诊断结果
            arrhythmia = analysis_data.get('ArrhythmiaDiagnosis', {})
            if arrhythmia:
                positive_diagnoses = [k for k, v in arrhythmia.items() if v == 1]
                if positive_diagnoses:
                    for i, diagnosis in enumerate(positive_diagnoses, 4):
                        self.logger.info(f"{i}. Diagnosis: {diagnosis}")
            
            # 记录健康指标
            health_metrics = analysis_data.get('HealthMetrics', {})
            if health_metrics:
                pressure = health_metrics.get('Pressure', 0)
                hrv = health_metrics.get('HRV', 0)
                fatigue = health_metrics.get('Fatigue', 0)
                vitality = health_metrics.get('Vitality', 0)
                
                self.logger.info(f"Health Metrics - Pressure: {pressure}, HRV: {hrv}, Fatigue: {fatigue}, Vitality: {vitality}")
            
            # 记录信号质量
            signal_quantity = analysis_data.get('SignalQuantity', 1)
            is_noise = analysis_data.get('IsNoise', False)
            noise_message = analysis_data.get('NoiseMessage', '')
            
            self.logger.info(f"Signal Quality: {signal_quantity}, Is Noise: {is_noise}")
            if noise_message:
                self.logger.info(f"Noise Message: {noise_message}")
            
            # 记录RR间期数据（如果存在）
            rr_intervals = analysis_data.get('RRIntervals', '')
            if rr_intervals:
                rr_list = rr_intervals.split(',')[:10]  # 只记录前10个
                self.logger.info(f"RR Intervals (first 10): {', '.join(rr_list)}")
            
            if union_id:
                self.logger.info(f"=== ECG Analysis End for User: {union_id} ===")
                
        except Exception as e:
            self.logger.error(f"Error logging heart rate analysis data: {str(e)}")
    
    def log_processing_step(self, step_name, data, union_id=None):
        """
        记录处理步骤数据
        
        Args:
            step_name: 步骤名称
            data: 步骤数据
            union_id: 用户ID
        """
        try:
            prefix = f"[{union_id}] " if union_id else ""
            self.logger.info(f"{prefix}{step_name}: {json.dumps(data, ensure_ascii=False)}")
        except Exception as e:
            self.logger.error(f"Error logging processing step: {str(e)}")
    
    def log_waveform_data(self, waveform_info, union_id=None):
        """
        记录波形分析数据
        
        Args:
            waveform_info: 波形信息字典
            union_id: 用户ID
        """
        try:
            prefix = f"[{union_id}] " if union_id else ""
            
            if isinstance(waveform_info, dict):
                # 记录关键波形指标
                if 'waveform' in waveform_info:
                    waveform = waveform_info['waveform']
                    
                    # 记录心率变异性数据
                    if 'rr_intervals' in waveform:
                        rr_intervals = waveform['rr_intervals']
                        if rr_intervals:
                            self.logger.info(f"{prefix}RR Intervals Count: {len(rr_intervals)}")
                            if len(rr_intervals) > 0:
                                self.logger.info(f"{prefix}RR Intervals Range: {min(rr_intervals):.3f} - {max(rr_intervals):.3f} s")
                    
                    # 记录NN间期数据
                    if 'nn_intervals' in waveform:
                        nn_intervals = waveform['nn_intervals']
                        if nn_intervals:
                            self.logger.info(f"{prefix}NN Intervals Count: {len(nn_intervals)}")
                    
                    # 记录R峰检测结果
                    if 'r_peaks' in waveform:
                        r_peaks = waveform['r_peaks']
                        if r_peaks:
                            self.logger.info(f"{prefix}R Peaks Detected: {len(r_peaks)}")
                
        except Exception as e:
            self.logger.error(f"Error logging waveform data: {str(e)}")
    
    def log_error(self, error_message, union_id=None):
        """
        记录错误信息
        
        Args:
            error_message: 错误消息
            union_id: 用户ID
        """
        try:
            prefix = f"[{union_id}] " if union_id else ""
            self.logger.error(f"{prefix}ECG Analysis Error: {error_message}")
        except Exception as e:
            # 避免日志记录错误导致的无限循环
            pass


# 创建全局实例
ecg_analysis_logger = ECGAnalysisDataLogger()
